"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { TERM_ENDPOINTS } from "../../endpoints";

export const getTermFile = async (idTermo: string | number): Promise<ApiResponseReturn<string>> => {
	const response = await createRequestAdmin<any>({
		method: "GET",
		path: TERM_ENDPOINTS.GET_FILE(idTermo),
		responseType: "arraybuffer",
	});

	if (!response.success) {
		return response;
	}

	let buffer: ArrayBuffer;

	// Se o servidor retornou um Buffer (Node.js)
	if (Buffer.isBuffer(response.data)) {
		// O Buffer contém uma string JSON com array de bytes
		const bufferString = response.data.toString("utf8");
		try {
			const bytesArray = JSON.parse(bufferString);
			if (Array.isArray(bytesArray)) {
				buffer = new Uint8Array(bytesArray).buffer;
			} else {
				throw new Error("Formato de dados inválido");
			}
		} catch (error) {
			console.error("Erro ao parsear dados do Buffer:", error);
			return {
				success: false,
				data: { message: "Formato de dados inválido retornado pelo servidor" },
				status: 500,
			};
		}
	}
	// Se o servidor retornou uma string JSON com array de bytes
	else if (typeof response.data === "string") {
		try {
			const bytesArray = JSON.parse(response.data);
			if (Array.isArray(bytesArray)) {
				buffer = new Uint8Array(bytesArray).buffer;
			} else {
				throw new Error("Formato de dados inválido");
			}
		} catch (error) {
			console.error("Erro ao parsear dados do servidor:", error);
			return {
				success: false,
				data: { message: "Formato de dados inválido retornado pelo servidor" },
				status: 500,
			};
		}
	}
	// Se o servidor retornou um array diretamente
	else if (Array.isArray(response.data)) {
		buffer = new Uint8Array(response.data).buffer;
	}
	// Se o servidor retornou um ArrayBuffer (comportamento esperado)
	else if (response.data instanceof ArrayBuffer) {
		buffer = response.data;
	}
	// Outros casos
	else {
		console.error("Tipo de dados não suportado:", typeof response.data);
		return {
			success: false,
			data: { message: "Tipo de dados não suportado retornado pelo servidor" },
			status: 500,
		};
	}

	const uint8Array = new Uint8Array(buffer);
	const base64 = Buffer.from(uint8Array).toString("base64");

	return {
		success: true,
		data: base64,
		status: response.status,
	};
};
