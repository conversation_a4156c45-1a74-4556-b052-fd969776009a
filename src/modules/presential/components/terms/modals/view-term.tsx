"use client";
import { useGetTermFileQuery } from "@/modules/presential/hooks/terms/get-term-file.hook";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";
import { Modal } from "@/shared/components/custom/modal";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { FileText, X } from "lucide-react";
import dynamic from "next/dynamic";

const PdfViewer = dynamic(() => import("@/modules/pdf/components/pdf"), {
	ssr: false,
	loading: () => (
		<div className="flex items-center justify-center h-96">
			<Skeleton className="w-full h-full" />
		</div>
	),
});

interface IViewTermModalProps {
	isOpen: boolean;
	onClose: () => void;
	term: ITerm | null;
}

export const ViewTermModal = ({ isOpen, onClose, term }: IViewTermModalProps) => {
	const { data: termFileData, isLoading, error } = useGetTermFileQuery(term?.id || 0, !!term && isOpen);

	const handleClose = () => {
		onClose();
	};

	if (!term) return null;

	return (
		<Modal isOpen={isOpen} onRequestClose={handleClose} shouldCloseOnOverlayClick={false} className="max-w-6xl w-[95vw] max-h-[95vh] h-[95vh]">
			<div className="flex flex-col h-full bg-white rounded-lg shadow-lg">
				<header className="flex items-center justify-between p-4 border-b border-gray-200">
					<div className="flex items-center gap-3">
						<div className="p-2 rounded-md bg-pormade/10 text-pormade">
							<FileText size={20} className="text-pormade" />
						</div>
						<div>
							<h2 className="text-lg font-semibold text-gray-900">Visualizar Termo</h2>
							<p className="text-sm text-gray-500 truncate max-w-md" title={term.title}>
								{term.title}
							</p>
						</div>
					</div>
					<Button variant="ghost" size="sm" className="hover:bg-gray-100" onClick={handleClose} aria-label="Fechar modal">
						<X size={20} />
					</Button>
				</header>

				<main className="flex-1 p-4 overflow-hidden">
					{isLoading && (
						<div className="flex items-center justify-center h-full">
							<div className="text-center">
								<Skeleton className="w-16 h-16 rounded-full mx-auto mb-4" />
								<p className="text-gray-500">Carregando termo...</p>
							</div>
						</div>
					)}

					{error && (
						<div className="flex items-center justify-center h-full">
							<div className="text-center">
								<FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
								<p className="text-red-500 font-medium">Erro ao carregar o termo</p>
								<p className="text-sm text-gray-500 mt-1">Não foi possível carregar o arquivo do termo</p>
							</div>
						</div>
					)}

					{termFileData && (
						<div className="h-full">
							<PdfViewer id={`term-${term.id}`} buffer={termFileData} isModal />
						</div>
					)}

					{!termFileData && !isLoading && !error && (
						<div className="flex items-center justify-center h-full">
							<div className="text-center">
								<FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
								<p className="text-gray-500">Nenhum arquivo disponível</p>
							</div>
						</div>
					)}
				</main>
			</div>
		</Modal>
	);
};
