import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import { useEffect, useMemo, useState } from "react";
import { PdfService } from "../../services/pdf-service";
import { pdfDocumentProxy } from "../../states/pdf-proxy.state";

interface LoadPdfProxyProps {
	id: string;
	buffer: ArrayBuffer;
	service?: PdfService;
}

interface ILoadPdfProxyReturn {
	clearCache: () => void;
	isLoading: boolean;
	error: string | null;
}

export const useLoadPdfProxy = ({ id, buffer, service }: LoadPdfProxyProps): ILoadPdfProxyReturn => {
	const setDocumentProxy = useSetAtom(pdfDocumentProxy);
	const pdfService = useMemo(() => service || new PdfService(), [service]);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		async function loadDocument(): Promise<void> {
			if (!buffer || buffer.byteLength === 0) {
				setDocumentProxy(null);
				setError("Buffer de PDF vazio ou inválido");
				return;
			}

			setIsLoading(true);
			setError(null);

			try {
				const uint8Array = new Uint8Array(buffer);
				console.log("uint8Array", uint8Array);
				const pdfHeader = String.fromCharCode(...uint8Array.slice(0, 4));

				if (pdfHeader !== "%PDF") {
					throw new Error("Arquivo não é um PDF válido");
				}

				const pdf = await pdfService.getPdfDocumentProxy({ id, buffer });
				setDocumentProxy(pdf ?? null);

				if (!pdf) {
					setError("Falha ao processar o documento PDF");
				}
			} catch (error) {
				console.error("Erro ao carregar o PDF:", error);

				let errorMessage = "Erro desconhecido ao carregar o PDF";

				if (error instanceof Error) {
					if (error.message.includes("Invalid PDF structure") || error.message.includes("InvalidPDFException")) {
						errorMessage = "Estrutura do PDF é inválida ou arquivo corrompido";
					} else if (error.message.includes("não é um PDF válido")) {
						errorMessage = "O arquivo não é um documento PDF válido";
					} else if (error.message.includes("Falha ao carregar o documento PDF")) {
						errorMessage = "Não foi possível processar o documento PDF";
					} else {
						errorMessage = error.message;
					}
				}

				setError(errorMessage);
				setDocumentProxy(null);
			} finally {
				setIsLoading(false);
			}
		}

		loadDocument();
	}, [id, buffer, pdfService, setDocumentProxy]);

	return {
		clearCache: (): void => pdfService.clearCache(),
		isLoading,
		error,
	};
};
