import { PDFDocumentProxy } from "pdfjs-dist";
import React, { memo, useMemo } from "react";
import PdfCanvas from "./pdf-canvas";

interface PdfViewerContentProps {
	pdfDocument: PDFDocumentProxy;
	totalPages: number;
	zoom: number;
	onRenderComplete: (pageNumber: number) => void;
	forceRenderAllPages: boolean;
}

const PdfViewerContent: React.FC<PdfViewerContentProps> = memo(({ pdfDocument, totalPages, zoom, onRenderComplete, forceRenderAllPages }) => {
	const pageNumbers = useMemo(() => Array.from({ length: totalPages }, (_, index) => index + 1), [totalPages]);

	return (
		<>
			{pageNumbers.map(pageNumber => (
				<PdfCanvas
					key={`pdf-page-${pageNumber}`}
					pdfDocument={pdfDocument}
					pageNumber={pageNumber}
					zoom={zoom}
					onRenderComplete={() => onRenderComplete(pageNumber)}
					forceRenderAllPages={forceRenderAllPages}
				/>
			))}
		</>
	);
});

PdfViewerContent.displayName = "PdfViewerContent";

export default PdfViewerContent;
